//! # 隧道管理模块
//!
//! 本模块提供了隧道连接的核心功能，包括：
//! - 隧道连接管理
//! - 多节点负载均衡
//! - 流量统计
//! - 连接状态监控
//! - 延迟测试

use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;

use serde_derive::Deserialize;
use tokio::sync::RwLock;
use tokio::time::sleep;
use uuid::Uuid;

use flyshadow_common::tunnel::tunnel_package::{PackageCmd, TunnelPackage};

use crate::mapper::tunnel_mapper_context::TunnelMapperContext;
use crate::port_forward::port_forward::PortForward;
use crate::tunnel::tunnel_connector::TunnelConnector;
use crate::util::node_encrypt_util::node_url_encrypt;

// ==================== 常量定义 ====================

/// 默认连接重试次数
const DEFAULT_RETRY_COUNT: u8 = 5;

/// 测试连接重试次数
const TEST_RETRY_COUNT: u8 = 1;

/// 单节点默认连接数
const SINGLE_NODE_CONNECTION_COUNT: u8 = 10;

/// 延迟测试超时时间（毫秒）
const PING_TEST_TIMEOUT_MS: u64 = 500;

/// 延迟测试最大等待次数
const PING_TEST_MAX_ATTEMPTS: u8 = 20;

// ==================== 数据结构定义 ====================

/// 连接信息结构体
///
/// 用于存储隧道连接所需的基本信息
#[derive(Deserialize, Clone)]
pub struct ConnectInfo {
    /// 服务器主机地址（支持多节点格式：host1:port1,host2:port2）
    pub host: String,
    /// 服务器端口号
    pub port: u16,
    /// 连接密码
    pub password: String,
}

/// 隧道连接状态枚举
///
/// 表示隧道当前的连接状态
#[derive(Copy, Clone, PartialEq, Debug)]
pub enum TunnelStatus {
    /// 连接成功，可以正常传输数据
    Success,
    /// 等待登录，正在进行身份验证
    WaitLogin,
    /// 已登出，连接已断开
    Logout,
}

// ==================== 核心结构体定义 ====================

/// 隧道管理器
///
/// 负责管理多个隧道连接器，提供负载均衡、流量统计、状态监控等功能
pub struct Tunnel {
    /// 隧道连接器列表，支持多连接负载均衡
    tunnel_connector: RwLock<Vec<Arc<TunnelConnector>>>,

    /// 隧道连接器映射表，用于快速查找特定连接
    /// Key格式："{source_address}==={protocol}"
    tunnel_connector_map: Arc<RwLock<HashMap<String, Arc<TunnelConnector>>>>,

    /// 隧道映射上下文，管理连接映射和路由
    tunnel_mapper_context: Arc<TunnelMapperContext>,

    /// 连接器轮询索引，用于负载均衡
    tunnel_connector_index: RwLock<usize>,

    /// 上传流量统计（字节）
    upload: Arc<RwLock<i64>>,

    /// 下载流量统计（字节）
    download: Arc<RwLock<i64>>,

    /// 隧道唯一标识符
    tunnel_uuid: RwLock<String>,

    /// 端口转发管理器
    pub port_forward: Arc<PortForward>,
}

// ==================== 静态方法实现 ====================

impl Tunnel {
    /// 测试隧道延迟（JSON格式输入）
    ///
    /// # 参数
    /// * `connect_info` - JSON格式的连接信息字符串
    ///
    /// # 返回值
    /// * `i64` - 延迟时间（毫秒），-1表示测试失败
    ///
    /// # 示例
    /// ```rust
    /// let json_info = r#"{"host":"***********","port":8080,"password":"test123"}"#;
    /// let delay = Tunnel::test_ping(json_info.to_string()).await;
    /// ```
    pub async fn test_ping(connect_info: String) -> i64 {
        let connect_info: ConnectInfo = match serde_json::from_str(&connect_info) {
            Ok(info) => info,
            Err(_) => {
                log::error!("解析连接信息JSON失败: {}", connect_info);
                return -1;
            }
        };
        Self::test_ping_by_struct(connect_info).await
    }

    /// 测试隧道延迟（结构体输入）
    ///
    /// # 参数
    /// * `connect_info` - 连接信息结构体
    ///
    /// # 返回值
    /// * `i64` - 延迟时间（毫秒），-1表示测试失败
    pub async fn test_ping_by_struct(connect_info: ConnectInfo) -> i64 {
        // 创建临时隧道实例用于测试
        let tunnel = Tunnel::new(Arc::new(TunnelMapperContext::new())).await;

        // 解析连接信息
        let mut hosts: Vec<(String, u16)> = Vec::new();
        Self::get_connect_info(connect_info.host, connect_info.port, &mut hosts, TEST_RETRY_COUNT);

        if hosts.is_empty() {
            log::warn!("无法解析主机信息");
            return -1;
        }

        // 使用第一个可用主机进行测试
        let (host, port) = hosts.into_iter().next().unwrap();

        // 建立测试连接
        tunnel.test_connect(host, port, connect_info.password).await;

        // 等待连接建立并测试延迟
        for attempt in 0..PING_TEST_MAX_ATTEMPTS {
            sleep(Duration::from_millis(PING_TEST_TIMEOUT_MS)).await;

            if tunnel.get_status().await == TunnelStatus::Success {
                let delay = tunnel.get_ping_delay().await;
                if delay > 0 {
                    let delay_ms = delay as i64;
                    log::info!("延迟测试成功: {}ms (尝试次数: {})", delay_ms, attempt + 1);
                    tunnel.disconnect().await;
                    return delay_ms;
                }
            }
        }

        log::warn!("延迟测试超时");
        tunnel.disconnect().await;
        -1
    }
}

// ==================== 测试代码 ====================

#[cfg(test)]
mod tests {
    use super::*;

    /// 测试连接信息解析功能
    #[test]
    fn test_get_connect_info() {
        let mut hosts: Vec<(String, u16)> = Vec::new();
        Tunnel::get_connect_info(
            "*************:6001,************:6001".to_string(),
            13751,
            &mut hosts,
            1,
        );
        println!("解析的主机列表: {:?}", hosts);
        assert!(!hosts.is_empty(), "主机列表不应为空");
    }
}

// ==================== 实例方法实现 ====================

impl Tunnel {
    /// 创建新的隧道实例
    ///
    /// # 参数
    /// * `tunnel_mapper_context` - 隧道映射上下文
    ///
    /// # 返回值
    /// * `Arc<Tunnel>` - 隧道实例的智能指针
    pub async fn new(tunnel_mapper_context: Arc<TunnelMapperContext>) -> Arc<Tunnel> {
        // 创建端口转发管理器
        let port_forward = PortForward::new();

        // 生成唯一的隧道标识符
        let tunnel_uuid = Uuid::new_v4()
            .simple()
            .encode_lower(&mut Uuid::encode_buffer())
            .to_string();

        // 创建隧道实例
        let tunnel = Arc::new(Tunnel {
            tunnel_connector: RwLock::new(Vec::new()),
            tunnel_connector_map: Arc::new(RwLock::new(HashMap::new())),
            tunnel_mapper_context,
            tunnel_connector_index: RwLock::new(0),
            upload: Arc::new(RwLock::new(0)),
            download: Arc::new(RwLock::new(0)),
            tunnel_uuid: RwLock::new(tunnel_uuid),
            port_forward: port_forward.clone(),
        });

        // 设置端口转发的隧道引用
        port_forward.set_tunnel(tunnel.clone()).await;

        log::info!("隧道实例创建成功，UUID: {}", tunnel.tunnel_uuid.read().await);
        tunnel
    }

    /// 解析连接信息，支持多节点和单节点格式
    ///
    /// # 参数
    /// * `host` - 主机地址字符串，支持格式：
    ///   - 单节点：`***********` 或 `example.com:8080`
    ///   - 多节点：`host1:port1,host2:port2,host3:port3`
    /// * `port` - 默认端口号（用于单节点或验证多节点）
    /// * `result` - 输出结果向量
    /// * `multiple` - 每个节点的连接倍数
    fn get_connect_info(host: String, port: u16, result: &mut Vec<(String, u16)>, multiple: u8) {
        if host.contains(',') {
            // 多节点模式：验证URL加密哈希
            if node_url_encrypt(&host) != port {
                log::warn!("多节点URL加密验证失败: {} != {}", node_url_encrypt(&host), port);
                return;
            }

            // 解析多个主机地址
            let hosts: Vec<String> = host
                .split(',')
                .map(|x| x.trim().to_string())
                .filter(|x| !x.is_empty())
                .collect();

            if hosts.is_empty() {
                log::warn!("多节点解析结果为空");
                return;
            }

            // 为每个主机创建多个连接
            for _ in 0..multiple {
                for host_addr in &hosts {
                    if let Some((parsed_host, parsed_port)) = Self::parse_host_port(host_addr) {
                        result.push((parsed_host, parsed_port));
                    }
                }
            }
        } else {
            // 单节点模式
            Self::handle_single_node(&host, port, result);
        }
    }

    /// 解析单个主机地址和端口
    ///
    /// # 参数
    /// * `host_addr` - 主机地址字符串，格式：`host:port` 或 `protocol:host:port`
    ///
    /// # 返回值
    /// * `Option<(String, u16)>` - 解析成功返回(主机, 端口)，失败返回None
    fn parse_host_port(host_addr: &str) -> Option<(String, u16)> {
        let parts: Vec<String> = host_addr
            .split(':')
            .map(|x| x.trim().to_string())
            .filter(|x| !x.is_empty())
            .collect();

        match parts.len() {
            // 格式：host:port
            2 => {
                if let Ok(port) = parts[1].parse::<u16>() {
                    Some((parts[0].clone(), port))
                } else {
                    log::warn!("端口解析失败: {}", parts[1]);
                    None
                }
            }
            // 格式：protocol:host:port 或更复杂的格式
            n if n >= 3 => {
                if let Ok(port) = parts[n - 1].parse::<u16>() {
                    let host = parts[..n - 1].join(":");
                    Some((host, port))
                } else {
                    log::warn!("复杂格式端口解析失败: {}", parts[n - 1]);
                    None
                }
            }
            _ => {
                log::warn!("主机地址格式不正确: {}", host_addr);
                None
            }
        }
    }

    /// 处理单节点连接信息
    ///
    /// # 参数
    /// * `host` - 主机地址
    /// * `port` - 端口号
    /// * `result` - 输出结果向量
    fn handle_single_node(host: &str, port: u16, result: &mut Vec<(String, u16)>) {
        let connection_count = SINGLE_NODE_CONNECTION_COUNT;

        if host.contains(':') {
            // 主机地址包含端口信息，直接使用
            let parts: Vec<String> = host
                .split(':')
                .map(|x| x.trim().to_string())
                .filter(|x| !x.is_empty())
                .collect();

            let full_host = parts.join(":");
            for _ in 0..connection_count {
                result.push((full_host.clone(), port));
            }
        } else {
            // 纯主机地址，使用提供的端口
            for _ in 0..connection_count {
                result.push((host.to_string(), port));
            }
        }
    }

    /// 建立隧道连接（生产环境）
    ///
    /// 创建多个连接器实现负载均衡和高可用性
    ///
    /// # 参数
    /// * `host` - 服务器主机地址
    /// * `port` - 服务器端口
    /// * `password` - 连接密码
    pub async fn connect(&self, host: String, port: u16, password: String) {
        log::info!("开始建立隧道连接: {}:{}", host, port);

        // 先断开现有连接
        self.disconnect().await;

        // 解析连接信息
        let mut hosts: Vec<(String, u16)> = Vec::new();
        Self::get_connect_info(host.clone(), port, &mut hosts, DEFAULT_RETRY_COUNT);

        if hosts.is_empty() {
            log::error!("无法解析连接信息: {}:{}", host, port);
            return;
        }

        log::info!("解析到 {} 个连接节点", hosts.len());

        // 获取隧道UUID
        let tunnel_uuid = self.tunnel_uuid.read().await.clone();

        // 为每个主机创建连接器
        let mut connectors = Vec::new();
        for (host_addr, host_port) in hosts {
            let tunnel_connector = Arc::new(TunnelConnector::new(
                tunnel_uuid.clone(),
                self.tunnel_mapper_context.clone(),
                self.tunnel_connector_map.clone(),
                self.port_forward.clone(),
            ));

            // 异步建立连接
            tunnel_connector.clone().connect(host_addr.clone(), host_port, password.clone()).await;
            connectors.push(tunnel_connector);

            log::debug!("创建连接器: {}:{}", host_addr, host_port);
        }

        // 将连接器添加到管理列表
        *self.tunnel_connector.write().await = connectors;
        log::info!("隧道连接建立完成");
    }

    /// 建立测试连接（仅用于延迟测试）
    ///
    /// 创建单个连接器用于测试网络延迟
    ///
    /// # 参数
    /// * `host` - 服务器主机地址
    /// * `port` - 服务器端口
    /// * `password` - 连接密码
    pub async fn test_connect(&self, host: String, port: u16, password: String) {
        log::debug!("建立测试连接: {}:{}", host, port);

        let tunnel_uuid = self.tunnel_uuid.read().await.clone();
        let tunnel_connector = Arc::new(TunnelConnector::new(
            tunnel_uuid,
            self.tunnel_mapper_context.clone(),
            self.tunnel_connector_map.clone(),
            self.port_forward.clone(),
        ));

        // 建立连接
        tunnel_connector.clone().connect(host, port, password).await;

        // 添加到连接器列表
        self.tunnel_connector.write().await.push(tunnel_connector);
    }

    /// 设置客户端UUID
    ///
    /// # 参数
    /// * `uuid` - 新的客户端唯一标识符
    pub async fn set_client_uuid(&self, uuid: String) {
        let old_uuid = self.tunnel_uuid.read().await.clone();
        *self.tunnel_uuid.write().await = uuid.clone();
        log::info!("客户端UUID已更新: {} -> {}", old_uuid, uuid);
    }

    /// 获取并重置上传流量统计
    ///
    /// # 返回值
    /// * `i64` - 自上次调用以来的上传字节数
    pub async fn get_upload(&self) -> i64 {
        // 获取本地统计并重置
        let mut local_upload = self.upload.write().await;
        let mut total_upload = *local_upload;
        *local_upload = 0;

        // 累加所有连接器的上传流量
        let connectors = self.tunnel_connector.read().await;
        for connector in connectors.iter() {
            total_upload += connector.get_upload().await;
        }

        if total_upload > 0 {
            log::debug!("上传流量统计: {} 字节", total_upload);
        }
        total_upload
    }

    /// 获取并重置下载流量统计
    ///
    /// # 返回值
    /// * `i64` - 自上次调用以来的下载字节数
    pub async fn get_download(&self) -> i64 {
        // 获取本地统计并重置
        let mut local_download = self.download.write().await;
        let mut total_download = *local_download;
        *local_download = 0;

        // 累加所有连接器的下载流量
        let connectors = self.tunnel_connector.read().await;
        for connector in connectors.iter() {
            total_download += connector.get_download().await;
        }

        if total_download > 0 {
            log::debug!("下载流量统计: {} 字节", total_download);
        }
        total_download
    }

    /// 增加上传流量统计
    ///
    /// # 参数
    /// * `bytes` - 要增加的字节数
    pub async fn add_upload_traffic(&self, bytes: i64) {
        if bytes > 0 {
            *self.upload.write().await += bytes;
        }
    }

    /// 增加下载流量统计
    ///
    /// # 参数
    /// * `bytes` - 要增加的字节数
    pub async fn add_download_traffic(&self, bytes: i64) {
        if bytes > 0 {
            *self.download.write().await += bytes;
        }
    }

    /// 获取隧道整体连接状态
    ///
    /// # 返回值
    /// * `TunnelStatus` - 隧道状态，只要有一个连接器成功就返回Success
    pub async fn get_status(&self) -> TunnelStatus {
        let connectors = self.tunnel_connector.read().await;

        // 检查是否有任何连接器处于成功状态
        for connector in connectors.iter() {
            if connector.get_status().await == TunnelStatus::Success {
                return TunnelStatus::Success;
            }
        }

        // 如果没有成功的连接器，返回等待登录状态
        TunnelStatus::WaitLogin
    }

    /// 获取网络延迟
    ///
    /// 返回第一个可用连接器的延迟时间
    ///
    /// # 返回值
    /// * `u128` - 延迟时间（毫秒），0表示无可用连接或未测量
    pub async fn get_ping_delay(&self) -> u128 {
        let connectors = self.tunnel_connector.read().await;

        if let Some(first_connector) = connectors.first() {
            first_connector.get_ping_delay().await
        } else {
            0
        }
    }

    /// 断开所有隧道连接
    ///
    /// 清理所有连接器、映射表和重置状态
    pub async fn disconnect(&self) {
        log::info!("开始断开隧道连接");

        // 断开所有连接器
        let mut connectors = self.tunnel_connector.write().await;
        let connector_count = connectors.len();

        for connector in connectors.drain(..) {
            connector.disconnect().await;
        }

        // 清理连接器映射表
        self.tunnel_connector_map.write().await.clear();

        // 重置轮询索引
        *self.tunnel_connector_index.write().await = 0;

        log::info!("隧道连接断开完成，共断开 {} 个连接器", connector_count);
    }

    /// 将数据包写入隧道
    ///
    /// 根据源地址和协议类型选择合适的连接器发送数据
    ///
    /// # 参数
    /// * `tunnel_package` - 要发送的隧道数据包
    ///
    /// # 返回值
    /// * `Result<(), String>` - 成功返回Ok(())，失败返回错误信息
    pub async fn write_to_tunnel(&self, tunnel_package: TunnelPackage) -> Result<(), String> {
        // 检查源地址是否存在
        let source_addr = tunnel_package.source_address.as_ref()
            .ok_or_else(|| "数据包缺少源地址信息".to_string())?;

        // 生成连接器映射键：源地址 + 协议类型
        let map_key = format!("{}==={:?}", source_addr, tunnel_package.protocol);

        match tunnel_package.cmd {
            // 处理关闭连接命令
            PackageCmd::CloseConnect => {
                self.handle_close_connection(map_key, tunnel_package).await
            }
            // 处理其他数据传输命令
            _ => {
                self.handle_data_transmission(map_key, tunnel_package).await
            }
        }
    }

    /// 处理关闭连接请求
    ///
    /// # 参数
    /// * `map_key` - 连接器映射键
    /// * `tunnel_package` - 隧道数据包
    async fn handle_close_connection(&self, map_key: String, tunnel_package: TunnelPackage) -> Result<(), String> {
        // 从映射表中移除并获取连接器
        if let Some(tunnel_connector) = self.tunnel_connector_map.write().await.remove(&map_key) {
            log::debug!("关闭连接: {}", map_key);
            tunnel_connector.write_to_tunnel(tunnel_package).await
        } else {
            log::warn!("尝试关闭不存在的连接: {}", map_key);
            Err("连接不存在，无法关闭".to_string())
        }
    }

    /// 处理数据传输请求
    ///
    /// # 参数
    /// * `map_key` - 连接器映射键
    /// * `tunnel_package` - 隧道数据包
    async fn handle_data_transmission(&self, map_key: String, tunnel_package: TunnelPackage) -> Result<(), String> {
        // 首先尝试从现有映射中获取连接器
        {
            let connector_map = self.tunnel_connector_map.read().await;
            if let Some(tunnel_connector) = connector_map.get(&map_key) {
                return tunnel_connector.write_to_tunnel(tunnel_package).await;
            }
        }

        // 如果映射中不存在，尝试获取任意可用连接器
        if let Some(tunnel_connector) = self.get_any_tunnel().await {
            // 将连接器添加到映射表
            self.tunnel_connector_map.write().await.insert(map_key.clone(), tunnel_connector.clone());

            log::debug!("为新连接分配连接器: {}", map_key);
            tunnel_connector.write_to_tunnel(tunnel_package).await
        } else {
            log::error!("无可用的隧道连接器");
            Err("无可用的隧道连接".to_string())
        }
    }

    /// 获取任意可用的隧道连接器（负载均衡）
    ///
    /// 使用轮询算法从可用连接器中选择一个处于成功状态的连接器
    ///
    /// # 返回值
    /// * `Option<Arc<TunnelConnector>>` - 可用连接器，None表示无可用连接
    async fn get_any_tunnel(&self) -> Option<Arc<TunnelConnector>> {
        let connectors = self.tunnel_connector.read().await;
        let connector_count = connectors.len();

        // 检查是否有可用连接器
        if connector_count == 0 {
            log::debug!("没有可用的隧道连接器");
            return None;
        }

        // 使用轮询算法选择连接器
        let mut index_guard = self.tunnel_connector_index.write().await;

        // 遍历所有连接器，寻找可用的
        for _ in 0..connector_count {
            // 重置索引如果超出范围
            if *index_guard >= connector_count {
                *index_guard = 0;
            }

            let current_index = *index_guard;
            *index_guard += 1;

            // 获取当前索引的连接器
            if let Some(connector) = connectors.get(current_index) {
                // 检查连接器状态
                if connector.get_status().await == TunnelStatus::Success {
                    log::debug!("选择连接器 {} (索引: {})", current_index, current_index);
                    return Some(connector.clone());
                }
            }
        }

        log::warn!("所有连接器都不可用");
        None
    }
}

