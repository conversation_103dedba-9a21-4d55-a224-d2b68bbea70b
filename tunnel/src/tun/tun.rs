use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};

use futures::{sink::SinkExt, stream::StreamExt};
use log::{debug, error};
use netstack_lwip::NetStack;
use tokio::spawn;
use tokio::sync::mpsc::{channel, Receiver, Sender};
use tokio::sync::RwLock;

use flyshadow_common::tunnel::tunnel_package::PackageProtocol;

use crate::context::context::TunnelContext;
use crate::context::proxy_type::ProxyType;
use crate::mapper::tunnel_mapper_info::TunnelMapperInfo;
use crate::util::process_util::get_process_name_by_port;

/// 封锁的DNS加密地址列表
const BLOCKED_DNS_ADDRESSES: [&str; 12] = [
    "*******:853", "*******:443",
    "*******:853", "*******:443",
    "*******:853", "*******:443",
    "***************:853", "***************:443",
    "*********:443", "*********:853",
    "*********:443", "*********:853"
];

/// 通道缓冲区大小
const CHANNEL_BUFFER_SIZE: usize = 100;

/// TUN设备结构体
///
/// 负责处理TUN设备的数据包，包括TCP和UDP流量的处理和转发
pub struct Tun {
    /// 隧道上下文，包含隧道相关的配置和状态
    tunnel_context: Arc<TunnelContext>,
    /// 从客户端接收数据的通道接收端
    client_receiver: RwLock<Receiver<Vec<u8>>>,
    /// 向TUN设备发送数据的通道发送端
    tun_sender: Sender<Vec<u8>>,
}

impl Tun {
    /// 创建新的TUN实例
    ///
    /// # 参数
    /// * `tunnel_context` - 隧道上下文，包含隧道相关的配置和状态
    ///
    /// # 返回
    /// 返回一个新的TUN实例
    pub async fn new(tunnel_context: Arc<TunnelContext>) -> Self {
        // 创建客户端数据通道
        let (client_sender, client_receiver) = channel::<Vec<u8>>(CHANNEL_BUFFER_SIZE);
        // 创建TUN设备数据通道
        let (tun_sender, tun_receiver) = channel::<Vec<u8>>(CHANNEL_BUFFER_SIZE);

        let tun = Tun {
            tunnel_context,
            client_receiver: RwLock::new(client_receiver),
            tun_sender,
        };

        // 启动TUN数据处理器
        tun.start_tun_data_join_handler(client_sender.clone(), tun_receiver).await;

        tun
    }

    /// 创建隧道映射信息的辅助函数
    ///
    /// # 参数
    /// * `protocol` - 协议类型（TCP或UDP）
    /// * `source_addr` - 源地址
    /// * `source_port` - 源端口
    /// * `target_addr` - 目标地址
    /// * `target_port` - 目标端口
    ///
    /// # 返回
    /// 返回配置好的TunnelMapperInfo实例
    fn create_tunnel_mapper_info(
        protocol: PackageProtocol,
        source_addr: String,
        source_port: u16,
        target_addr: String,
        target_port: u16,
    ) -> TunnelMapperInfo {
        TunnelMapperInfo {
            protocol,
            source_addr,
            source_port,
            target_addr,
            target_port,
            fake_target_addr: "".to_string(),
            fake_target_port: 0,
            process_name: get_process_name_by_port(source_port, protocol),
            matcher_name: "".to_string(),
            matcher_rule: "".to_string(),
            proxy_type: ProxyType::Proxy,
            active_time: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis(),
            temp_data: vec![],
            direct_conn_priority: true,
            traffic_info: Arc::new(Default::default()),
        }
    }


    /// 启动TUN数据包处理协程
    ///
    /// 创建并启动多个异步任务，处理TUN设备的数据包，包括：
    /// 1. 从网络栈读取数据包并发送到TUN设备
    /// 2. 从TUN设备读取数据包并发送到网络栈
    /// 3. 处理TCP连接
    /// 4. 处理UDP数据包
    ///
    /// # 参数
    /// * `client_sender` - 向客户端发送数据的通道发送端
    /// * `tun_receiver` - 从TUN设备接收数据的通道接收端
    async fn start_tun_data_join_handler(
        &self,
        client_sender: Sender<Vec<u8>>,
        mut tun_receiver: Receiver<Vec<u8>>,
    ) {
        let tunnel_context = self.tunnel_context.clone();

        // 创建网络栈，设置TCP和UDP缓冲区大小
        let (stack, mut tcp_listener, udp_socket) = NetStack::with_buffer_size(512, 256).unwrap();
        let (mut stack_sink, mut stack_stream) = stack.split();

        // 创建异步任务列表
        let mut futs: Vec<futures::future::BoxFuture<'static, ()>> = Vec::new();

        // 任务1: 从网络栈读取数据包并发送到TUN设备
        futs.push(Box::pin(async move {
            while let Some(pkt) = stack_stream.next().await {
                if let Ok(pkt) = pkt {
                    if let Err(e) = client_sender.send(pkt).await {
                        error!("客户端发送数据错误: {}", e);
                        return;
                    }
                }
            }
        }));

        // 任务2: 从TUN设备读取数据包并发送到网络栈
        futs.push(Box::pin(async move {
            while let Some(data) = tun_receiver.recv().await {
                if let Err(e) = stack_sink.send(data).await {
                    error!("网络栈发送数据错误: {}", e);
                }
            }
        }));

        // 任务3: 处理TCP连接
        let tunnel_context_clone = tunnel_context.clone();
        futs.push(Box::pin(async move {
            while let Some((stream, local_addr, remote_addr)) = tcp_listener.next().await {
                // 检查是否为需要封锁的DNS加密地址
                let remote_addr_str = remote_addr.to_string();
                if BLOCKED_DNS_ADDRESSES.contains(&remote_addr_str.as_str()) {
                    // 跳过处理DNS加密地址
                    continue;
                }

                let tunnel_context_clone = tunnel_context_clone.clone();
                spawn(async move {
                    debug!("TCP接收客户端连接 {}--->{}",local_addr, remote_addr);

                    // 创建隧道映射信息
                    let tunnel_mapper_info = Self::create_tunnel_mapper_info(
                        PackageProtocol::TCP,
                        local_addr.ip().to_string(),
                        local_addr.port(),
                        remote_addr.ip().to_string(),
                        remote_addr.port(),
                    );

                    // 连接到服务器
                    if let Err(e) = tunnel_context_clone.tun_connect_server(tunnel_mapper_info, stream).await {
                        error!("连接断开错误: {}", e);
                        return;
                    }
                });
            }
        }));

        // 任务4: 处理UDP数据包
        // 在网络栈和NAT管理器之间接收和发送UDP数据包
        // NAT管理器维护UDP会话并将它们发送到分发器
        futs.push(Box::pin(async move {
            // 分离UDP socket的读写部分
            let (writer, mut reader) = udp_socket.split();
            let writer = Arc::new(writer);

            // 处理UDP数据包
            while let Ok((udp_msg, src_addr, dst_addr)) = reader.recv_from().await {
                // 检查是否为需要封锁的DNS加密地址
                let dst_addr_str = dst_addr.to_string();
                if BLOCKED_DNS_ADDRESSES.contains(&dst_addr_str.as_str()) {
                    // 跳过处理DNS加密地址
                    continue;
                }

                // 创建隧道映射信息
                let tunnel_mapper_info = Self::create_tunnel_mapper_info(
                    PackageProtocol::UDP,
                    src_addr.ip().to_string(),
                    src_addr.port(),
                    dst_addr.ip().to_string(),
                    dst_addr.port(),
                );

                // 发送UDP数据到服务端
                tunnel_context.tun_udp_write_to_server(
                    tunnel_mapper_info,
                    writer.clone(),
                    udp_msg.as_slice(),
                ).await;
            }
        }));

        // 启动所有任务并等待任何一个完成
        spawn(async move {
            futures::future::select_all(futs).await;
        });
    }

    /// 获取需要发送到TUN网卡的数据
    ///
    /// 从客户端接收通道获取数据，如果通道已关闭则返回空向量
    ///
    /// # 返回
    /// 返回从客户端接收到的数据包，如果没有数据则返回空向量
    pub async fn get_tun_data(&self) -> Vec<u8> {
        if let Some(data) = self.client_receiver.write().await.recv().await {
            data
        } else {
            vec![]
        }
    }

    /// 处理TUN数据包
    ///
    /// 将数据发送到TUN设备处理
    ///
    /// # 参数
    /// * `data` - 要处理的数据包
    pub async fn handler_tun_data(&self, data: Vec<u8>) {
        // 忽略发送错误，因为接收端可能已关闭
        let _ = self.tun_sender.send(data).await;
    }
}
