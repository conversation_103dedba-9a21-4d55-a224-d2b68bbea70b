//! # 隧道映射器信息模块
//!
//! 本模块定义了隧道映射器的信息结构，包括连接元数据、流量统计和序列化支持。

use flyshadow_common::tunnel::tunnel_package::PackageProtocol;
use serde::Serialize;
use std::sync::Arc;
use tokio::sync::RwLock;

use crate::context::proxy_type::ProxyType;

/// 隧道映射器信息结构
///
/// 存储单个隧道连接的所有相关信息，包括网络参数、代理配置、
/// 匹配规则和流量统计等。
#[derive(Clone, Debug)]
pub struct TunnelMapperInfo {
    /// 网络协议类型（TCP/UDP等）
    pub protocol: PackageProtocol,
    /// 源地址（客户端地址）
    pub source_addr: String,
    /// 源端口（客户端端口）
    pub source_port: u16,
    /// 目标地址（服务器地址）
    pub target_addr: String,
    /// 目标端口（服务器端口）
    pub target_port: u16,
    /// 伪造的目标地址（用于 Fake IP）
    pub fake_target_addr: String,
    /// 伪造的目标端口（用于 Fake IP）
    pub fake_target_port: u16,
    /// 发起连接的进程名称
    pub process_name: String,
    /// 匹配的规则名称
    pub matcher_name: String,
    /// 具体的匹配规则内容
    pub matcher_rule: String,
    /// 代理类型（直连、隧道等）
    pub proxy_type: ProxyType,
    /// 连接活跃时间戳
    pub active_time: u128,
    /// 临时数据缓存（用于连接建立时的数据）
    pub temp_data: Vec<u8>,
    /// 是否启用直连优先模式
    pub direct_conn_priority: bool,
    /// 流量统计信息
    pub traffic_info: Arc<TunnelMapperTrafficInfo>,
}

/// 隧道映射器流量统计信息
///
/// 记录连接的上传和下载流量，支持实时统计和总量统计。
/// 使用 Arc<RwLock<>> 确保多线程安全访问。
#[derive(Default, Clone, Debug)]
pub struct TunnelMapperTrafficInfo {
    /// 当前周期的上传流量（字节）
    upload_traffic: Arc<RwLock<u128>>,
    /// 当前周期的下载流量（字节）
    download_traffic: Arc<RwLock<u128>>,
    /// 总上传流量（字节）
    upload_traffic_total: Arc<RwLock<u128>>,
    /// 总下载流量（字节）
    download_traffic_total: Arc<RwLock<u128>>,
}

/// 隧道映射器信息的序列化结构
///
/// 用于将 TunnelMapperInfo 转换为可序列化的格式，
/// 主要用于 API 响应和日志记录。
#[derive(Serialize, Clone, Debug)]
pub struct TunnelMapperInfoSerialize {
    /// 网络协议类型
    protocol: PackageProtocol,
    /// 源地址
    source_addr: String,
    /// 源端口
    source_port: u16,
    /// 目标地址
    target_addr: String,
    /// 目标端口
    target_port: u16,
    /// 伪造的目标地址
    fake_target_addr: String,
    /// 伪造的目标端口
    fake_target_port: u16,
    /// 进程名称
    process_name: String,
    /// 匹配规则名称
    matcher_name: String,
    /// 匹配规则内容
    matcher_rule: String,
    /// 代理类型
    proxy_type: ProxyType,
    /// 活跃时间
    pub(crate) active_time: u128,
    /// 当前周期上传流量
    upload_traffic: u128,
    /// 当前周期下载流量
    download_traffic: u128,
    /// 总上传流量
    upload_traffic_total: u128,
    /// 总下载流量
    download_traffic_total: u128,
}

impl TunnelMapperInfoSerialize {
    /// 从 TunnelMapperInfo 创建序列化结构
    ///
    /// 异步读取流量统计信息并创建可序列化的结构体。
    ///
    /// # 参数
    /// * `tunnel_mapper_info` - 要序列化的隧道映射器信息
    ///
    /// # 返回值
    /// 返回包含所有信息的序列化结构
    pub async fn from(tunnel_mapper_info: TunnelMapperInfo) -> TunnelMapperInfoSerialize {
        // 异步读取流量统计信息
        let upload_traffic = *tunnel_mapper_info.traffic_info.upload_traffic.read().await;
        let download_traffic = *tunnel_mapper_info.traffic_info.download_traffic.read().await;
        let upload_traffic_total = *tunnel_mapper_info.traffic_info.upload_traffic_total.read().await;
        let download_traffic_total = *tunnel_mapper_info.traffic_info.download_traffic_total.read().await;

        TunnelMapperInfoSerialize {
            protocol: tunnel_mapper_info.protocol,
            source_addr: tunnel_mapper_info.source_addr,
            source_port: tunnel_mapper_info.source_port,
            target_addr: tunnel_mapper_info.target_addr,
            target_port: tunnel_mapper_info.target_port,
            fake_target_addr: tunnel_mapper_info.fake_target_addr,
            fake_target_port: tunnel_mapper_info.fake_target_port,
            process_name: tunnel_mapper_info.process_name,
            matcher_name: tunnel_mapper_info.matcher_name,
            matcher_rule: tunnel_mapper_info.matcher_rule,
            proxy_type: tunnel_mapper_info.proxy_type,
            active_time: tunnel_mapper_info.active_time,
            upload_traffic,
            download_traffic,
            upload_traffic_total,
            download_traffic_total,
        }
    }
}

impl TunnelMapperTrafficInfo {
    /// 添加上传流量
    ///
    /// 同时更新当前周期流量和总流量统计。
    ///
    /// # 参数
    /// * `upload_traffic` - 要添加的上传流量字节数
    pub async fn add_upload_traffic(&self, upload_traffic: u128) {
        *self.upload_traffic.write().await += upload_traffic;
        *self.upload_traffic_total.write().await += upload_traffic;
    }

    /// 添加下载流量
    ///
    /// 同时更新当前周期流量和总流量统计。
    ///
    /// # 参数
    /// * `download_traffic` - 要添加的下载流量字节数
    pub async fn add_download_traffic(&self, download_traffic: u128) {
        *self.download_traffic.write().await += download_traffic;
        *self.download_traffic_total.write().await += download_traffic;
    }

    /// 重置当前周期流量统计
    ///
    /// 将当前周期的上传和下载流量重置为 0，
    /// 但保留总流量统计。通常在统计周期结束时调用。
    pub async fn reset(&self) {
        *self.upload_traffic.write().await = 0;
        *self.download_traffic.write().await = 0;
    }

    /// 获取当前流量统计快照
    ///
    /// 返回当前的流量统计信息，用于监控和报告。
    ///
    /// # 返回值
    /// 返回 (当前上传, 当前下载, 总上传, 总下载) 的元组
    pub async fn get_traffic_snapshot(&self) -> (u128, u128, u128, u128) {
        let upload = *self.upload_traffic.read().await;
        let download = *self.download_traffic.read().await;
        let upload_total = *self.upload_traffic_total.read().await;
        let download_total = *self.download_traffic_total.read().await;
        (upload, download, upload_total, download_total)
    }
}