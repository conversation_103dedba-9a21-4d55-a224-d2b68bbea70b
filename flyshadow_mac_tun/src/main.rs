use chacha20::cipher::{KeyIvInit, StreamCipher};
use chacha20::ChaCha20;
use std::fs;
use std::process::Command;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::UnixStream;
use tokio::spawn;

// 常量定义
const ENCRYPTION_KEY: &[u8; 32] = b"01234567890123456789012345678901";
const ENCRYPTION_NONCE: &[u8; 12] = b"flyshadow123";
const BUFFER_SIZE: usize = 8096;
const SOCKET_PATH: &str = "/tmp/flyshadow_mac_tun.sock";

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    let default_gateway = if let Some(default_gateway) = get_default_gateway() {
        default_gateway
    } else {
        return Err(anyhow::anyhow!("No default gateway found."));
    };

    let mut config = tun::Configuration::default();
    config
        .address((10, 172, 50, 2))
        .netmask((255, 255, 255, 0))
        .destination((10, 172, 50, 1))
        .up();

    let device = tun::create_as_async(&config)?;

    println!("Create new tun success");
    setup_routing(default_gateway.as_str())?;
    setup_dns_resolver()?;

    let (mut device_reader, mut device_writer) = tokio::io::split(device);

    let socket = UnixStream::connect(SOCKET_PATH).await
        .map_err(|e| anyhow::anyhow!("Failed to connect to socket {}: {}", SOCKET_PATH, e))?;
    let (mut read_half, mut write_half) = tokio::io::split(socket);

    spawn(async move {
        let mut buf = [0u8; BUFFER_SIZE];
        loop {
            match device_reader.read(&mut buf).await {
                Ok(0) => {
                    break;
                }
                Ok(n) => {
                    let mut vec = buf[..n].to_vec();
                    // let hex_str = encode(&vec);
                    // println!("ReadFromDevice: {} n:{}", hex_str,n);
                    encrypt(&mut vec);

                    if let Err(e) = write_half.write_u32(vec.len() as u32).await {
                        eprintln!("Failed to write header: {}", e);
                        break;
                    }
                    if let Err(e) = write_half.write_all(&vec).await {
                        eprintln!("Failed to write data: {}", e);
                        break;
                    }
                }
                Err(e) => {
                    eprintln!("Failed to read from device: {}", e);
                }
            }
        }
    });

    println!("successfully connected to server");

    loop {
        // 读取 4 字节长度头
        let packet_len = match read_half.read_u32().await {
            Ok(packet_len) => packet_len as usize,
            Err(e) => {
                eprintln!("Failed to read header: {}", e);
                break;
            }
        };

        let mut packet_buf = vec![0u8; packet_len];
        if let Err(e) = read_half.read_exact(&mut packet_buf).await {
            eprintln!("Failed to read full packet: {}", e);
            break;
        }

        decrypt(&mut packet_buf);
        // let hex_str = encode(&packet_buf);
        // println!("WriteToDevice: {}", hex_str);
        if let Err(e) = device_writer.write_all(&packet_buf).await {
            eprintln!("Failed to write to device: {}", e);
            break;
        }
    }

    Ok(())
}

/// 添加一个路由条目
fn add_route(dest: &str, via: &str, is_interface: bool) -> anyhow::Result<()> {
    let args = ["-n", "add", "-net", dest, via];

    let status = Command::new("route")
        .args(&args)
        .status()
        .map_err(|e| anyhow::anyhow!("Failed to execute route command: {}", e))?;

    if status.success() {
        println!(
            "Added route to {} via {} (interface: {}), success: true",
            dest, via, is_interface
        );
    } else {
        return Err(anyhow::anyhow!(
            "Failed to add route to {} via {} (interface: {})",
            dest, via, is_interface
        ));
    }

    Ok(())
}

/// 添加默认 TUN 路由并排除本地网段
fn setup_routing(real_gateway: &str) -> anyhow::Result<()> {
    // 默认全局走 TUN
    add_route("**********/16", "***********", true)?;

    // 分割全球 IP 地址空间，避免与私有网段冲突
    let tun_routes = [
        "*******/8", "*******/7", "*******/6", "*******/5",
        "********/4", "********/3", "********/2", "*********/1"
    ];

    for route in &tun_routes {
        add_route(route, "***********", true)?;
    }

    // 排除本地回环与私有网段，走原网关
    let local_routes = [
        "*********/8",      // 本地回环
        "10.0.0.0/8",       // 私有网段 A
        "***********/16",   // 私有网段 C
        "**********/12"     // 私有网段 B
    ];

    for route in &local_routes {
        add_route(route, real_gateway, false)?;
    }

    flush_dns_cache()?;
    Ok(())
}

/// 刷新 DNS 缓存
fn flush_dns_cache() -> anyhow::Result<()> {
    let status = Command::new("dscacheutil")
        .args(&["-flushcache"])
        .status()
        .map_err(|e| anyhow::anyhow!("Failed to execute dscacheutil command: {}", e))?;

    if status.success() {
        println!("Execute dscacheutil flushcache, success: true");
    } else {
        return Err(anyhow::anyhow!("dscacheutil flushcache command failed"));
    }

    Ok(())
}

/// 设置 DNS 解析器，确保 nameserver *********** 在 /etc/resolv.conf 的第一行
fn setup_dns_resolver() -> anyhow::Result<()> {
    const RESOLV_CONF_PATH: &str = "/etc/resolv.conf";
    const TARGET_NAMESERVER: &str = "nameserver ***********";

    // 读取现有的 resolv.conf 文件内容
    let content = match fs::read_to_string(RESOLV_CONF_PATH) {
        Ok(content) => content,
        Err(e) => {
            eprintln!("Warning: Failed to read {}: {}", RESOLV_CONF_PATH, e);
            String::new()
        }
    };

    let lines: Vec<&str> = content.lines().collect();

    // 检查第一行是否已经是目标 nameserver
    if let Some(first_line) = lines.first() {
        if first_line.trim() == TARGET_NAMESERVER {
            println!("DNS resolver already configured correctly");
            return Ok(());
        }
    }

    // 需要插入目标 nameserver 到第一行
    let mut new_content = String::new();
    new_content.push_str(TARGET_NAMESERVER);
    new_content.push('\n');

    // 添加原有内容（如果第一行不是我们要的 nameserver）
    for line in &lines {
        if line.trim() != TARGET_NAMESERVER {
            new_content.push_str(line);
            new_content.push('\n');
        }
    }

    // 写入新内容到 resolv.conf
    match fs::write(RESOLV_CONF_PATH, new_content) {
        Ok(()) => {
            println!("Successfully updated {} with nameserver ***********", RESOLV_CONF_PATH);
            Ok(())
        }
        Err(e) => {
            Err(anyhow::anyhow!("Failed to write to {}: {}", RESOLV_CONF_PATH, e))
        }
    }
}

fn get_default_gateway() -> Option<String> {
    let output = Command::new("route")
        .args(&["-n", "get", "default"])
        .output()
        .ok()?;

    let stdout = String::from_utf8_lossy(&output.stdout);
    for line in stdout.lines() {
        if line.trim_start().starts_with("gateway:") {
            return Some(line.trim().split_whitespace().nth(1)?.to_string());
        }
    }
    None
}

fn encrypt(data: &mut [u8]) {
    let mut cipher = ChaCha20::new(ENCRYPTION_KEY.into(), ENCRYPTION_NONCE.into());
    cipher.apply_keystream(data);
}

fn decrypt(data: &mut [u8]) {
    encrypt(data); // ChaCha20 对称，同样的函数
}
