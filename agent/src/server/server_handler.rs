// 导入必要的依赖
use flyshadow_common::tunnel::package_encryption::{decrypt_package, encrypt_package};
use flyshadow_common::tunnel::tunnel_package::{PackageCmd, PackageProtocol, TunnelPackage};
use futures_util::stream::SplitSink;
use futures_util::{SinkExt, StreamExt};
use log::{debug, error, warn};
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::Duration;
use tokio::spawn;
use tokio::sync::mpsc::{unbounded_channel, UnboundedSender};
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use tokio::time::sleep;
use uuid::Uuid;
use warp::ws::{Message, WebSocket};

use crate::context::context::AgentContext;
use crate::route_table::route_table::RouteTable;
use crate::route_table::router::Route;

/// 服务器处理器结构体
/// 负责处理客户端连接、消息转发、用户认证等核心功能
pub struct ServerHandler {
    /// 客户端唯一标识符
    client_uuid: RwLock<String>,
    /// 服务器唯一标识符
    server_uuid: String,
    /// 客户端消息发送器
    client_sender: UnboundedSender<Vec<u8>>,
    /// 用户ID
    uid: RwLock<Option<i32>>,
    /// 用户密码MD5哈希值
    passwd_md5: RwLock<Option<String>>,
    /// 登录状态标识
    is_login: RwLock<bool>,
    /// 客户端套接字地址
    socket_addr: SocketAddr,
    /// 代理上下文，包含全局状态和配置
    agent_context: Arc<AgentContext>,
    /// WebSocket写入器，用于向客户端发送消息
    client_writer: Arc<RwLock<Option<SplitSink<WebSocket, Message>>>>,
    /// 读取缓冲区任务句柄
    read_buffer_job: RwLock<Option<JoinHandle<()>>>,
    /// 心跳检测任务句柄
    ping_job: RwLock<Option<JoinHandle<()>>>,
    /// 登录检查任务句柄
    login_job: RwLock<Option<JoinHandle<()>>>,
    /// 服务器接收器任务句柄
    server_receiver_job: RwLock<Option<JoinHandle<()>>>,
}

impl ServerHandler {
    /// 生成UUID字符串
    fn generate_uuid() -> String {
        Uuid::new_v4().simple().encode_lower(&mut Uuid::encode_buffer()).to_string()
    }

    /// 处理WebSocket连接的主要入口函数
    ///
    /// # 参数
    /// * `ws` - WebSocket连接
    /// * `path_param` - 路径参数，用于身份验证
    /// * `addr` - 客户端地址
    /// * `agent_context` - 代理上下文
    pub async fn handle_connection(
        ws: WebSocket,
        path_param: String,
        addr: Option<SocketAddr>,
        agent_context: Arc<AgentContext>
    ) {
        debug!("客户端连接，路径参数: {}", path_param);

        // 检查客户端地址是否有效
        let socket_addr = match addr {
            Some(addr) => addr,
            None => {
                error!("客户端地址为空");
                return;
            }
        };

        // 验证用户密码MD5
        let user_passwd_md5_list = agent_context.get_remote_user_passwd_md5_list().await;
        let passwd_md5 = match user_passwd_md5_list
            .iter()
            .find(|p| p.as_str() == path_param.as_str()) {
            Some(p) => p,
            None => {
                debug!("无效的认证参数: {} {}", path_param, socket_addr);
                return;
            }
        };

        // 分离WebSocket的读写端
        let (tx, mut rx) = ws.split();
        let (client_sender, mut server_receiver) = unbounded_channel::<Vec<u8>>();

        // 创建服务器处理器实例
        let server_handler = Arc::new(ServerHandler {
            client_uuid: RwLock::new(Self::generate_uuid()),
            server_uuid: Self::generate_uuid(),
            client_sender,
            uid: RwLock::new(None),
            passwd_md5: RwLock::new(None),
            is_login: RwLock::new(false),
            socket_addr,
            agent_context,
            client_writer: Arc::new(RwLock::new(Some(tx))),
            read_buffer_job: RwLock::new(None),
            ping_job: RwLock::new(None),
            login_job: RwLock::new(None),
            server_receiver_job: RwLock::new(None),
        });

        // 启动服务器接收器任务：转发客户端请求
        let client_writer = server_handler.client_writer.clone();
        let _ = server_handler.server_receiver_job.write().await.insert(spawn(async move {
            while let Some(data) = server_receiver.recv().await {
                if let Some(client_writer) = client_writer.write().await.as_mut() {
                    if let Err(e) = client_writer.send(Message::binary(data)).await {
                        debug!("发送消息到客户端失败: {}", e);
                        break;
                    }
                } else {
                    debug!("客户端写入器已关闭，停止转发");
                    break;
                }
            }
        }));

        // 启动心跳检测任务：每10分钟发送一次PING
        let server_handler_clone = server_handler.clone();
        let _ = server_handler.ping_job.write().await.insert(spawn(async move {
            loop {
                sleep(Duration::from_secs(60 * 10)).await;
                let mut ping_package = TunnelPackage::new(
                    PackageCmd::PING,
                    PackageProtocol::TCP,
                    None,
                    None,
                    None
                );
                if !server_handler_clone.write_tunnel_package(&mut ping_package).await {
                    debug!("心跳发送失败，关闭连接");
                    server_handler_clone.close().await;
                    break;
                }
            }
        }));

        // 主消息处理循环
        while let Some(result) = rx.next().await {
            match result {
                Ok(message) => {
                    if message.is_binary() {
                        // 解密并处理隧道包
                        match decrypt_package(message.as_bytes(), passwd_md5.as_bytes()) {
                            Ok(package) => {
                                if let Some(tunnel_package) = package {
                                    handler_tunnel_package(tunnel_package, server_handler.clone()).await;
                                } else {
                                    debug!("收到空的隧道包，断开连接");
                                    break;
                                }
                            }
                            Err(e) => {
                                error!("解密包失败: {}", e);
                                break;
                            }
                        }
                    } else if message.is_close() {
                        debug!("收到关闭消息，断开连接");
                        break;
                    }
                    // 忽略其他类型的消息（如文本消息）
                }
                Err(e) => {
                    error!("连接错误: {:?}", e);
                    break;
                }
            }
        }

        // 清理资源并关闭连接
        server_handler.close().await;
    }
}

impl ServerHandler {
    /// 处理用户登录请求
    ///
    /// # 参数
    /// * `tunnel_package` - 包含登录信息的隧道包
    ///
    /// # 返回值
    /// * `Some(String)` - 登录成功时返回用户密码
    /// * `None` - 登录失败
    pub async fn do_login(&self, mut tunnel_package: TunnelPackage) -> Option<String> {
        // 检查包数据是否存在
        let data = match tunnel_package.data.as_ref() {
            Some(data) => data,
            None => {
                warn!("登录失败：包数据为空");
                self.close().await;
                return None;
            }
        };

        // 解析用户密码
        let user_password = String::from_utf8_lossy(data.as_slice()).to_string();

        // 验证用户信息
        let (uid, password_md5) = self.agent_context.get_remote_user_info(&user_password).await;

        // 检查用户ID是否有效
        if uid == -1 {
            warn!("用户 [{}] 登录失败，密码 [{}] 错误", uid, user_password);
            self.close().await;
            return None;
        }

        // 保存用户信息
        *self.passwd_md5.write().await = Some(password_md5);
        *self.uid.write().await = Some(uid);

        // 设置用户在线状态
        self.agent_context.set_user_login(
            &uid,
            true,
            self.socket_addr.ip().to_string(),
            self.client_uuid.read().await.clone()
        ).await;

        // 更新登录状态
        *self.is_login.write().await = true;

        // 发送登录成功响应
        tunnel_package.cmd = PackageCmd::LoginSuccess;
        if !self.write_tunnel_package(&mut tunnel_package).await {
            warn!("用户 [{}] 登录失败：发送登录成功命令失败", uid);
            self.close().await;
            return None;
        }

        debug!("用户 [{}] 登录成功", uid);
        Some(user_password)
    }

    /// 定期检查用户登录状态
    /// 每60秒验证一次用户密码，确保用户仍然有效
    ///
    /// # 参数
    /// * `user_password` - 用户密码，用于验证
    async fn do_check_login(&self, user_password: String) {
        loop {
            sleep(Duration::from_secs(60)).await;

            // 重新验证用户信息
            let (uid, _password_md5) = self.agent_context.get_remote_user_info(&user_password).await;

            if uid == -1 {
                // 用户验证失败，可能密码已被修改或用户被禁用
                let current_uid = self.uid.read().await.unwrap_or(-1);
                warn!("用户 [{}] 登录状态检查失败，密码 [{}] 无效", current_uid, user_password);
                self.close().await;
                return;
            } else {
                // 先获取需要的数据，避免在调用 set_user_login 时持有锁
                let (current_uid, client_uuid) = {
                    let uid = self.uid.read().await.unwrap_or(-1);
                    let uuid = self.client_uuid.read().await.clone();
                    (uid, uuid)
                };

                // 更新用户在线状态
                self.agent_context.set_user_login(
                    &current_uid,
                    true,
                    self.socket_addr.ip().to_string(),
                    client_uuid
                ).await;
                debug!("用户 [{}] 登录状态检查通过", current_uid);
            }
        }
    }

    /// 获取当前连接的路由表
    /// 如果路由表不存在则创建一个新的
    async fn get_route_table(&self) -> Arc<RouteTable> {
        self.agent_context.route_context.get_or_create(&self.server_uuid).await.clone()
    }

    /// 删除当前连接的路由表
    /// 在连接关闭时调用，清理相关资源
    async fn del_route_table(&self) {
        self.agent_context.route_context.remove(&self.server_uuid).await;
    }

    /// 处理客户端PING请求
    /// 响应PONG并更新客户端UUID映射
    ///
    /// # 参数
    /// * `tunnel_package` - 包含客户端UUID的PING包
    pub async fn do_ping(&self, mut tunnel_package: TunnelPackage) {
        // 设置响应命令为PONG
        tunnel_package.cmd = PackageCmd::PONG;

        // 处理客户端UUID映射
        if let Some(uuid_data) = tunnel_package.data.take() {
            let client_uuid = String::from_utf8_lossy(&uuid_data).to_string();

            // 添加服务器映射，用于消息转发
            self.agent_context.server_mapper.add_mapper(
                &client_uuid,
                &self.server_uuid,
                self.client_sender.clone()
            ).await;

            // 更新客户端UUID
            *self.client_uuid.write().await = client_uuid;
            debug!("更新客户端UUID映射: {}", self.client_uuid.read().await);
        }

        // 发送PONG响应
        if !self.write_tunnel_package(&mut tunnel_package).await {
            debug!("发送PONG响应失败");
            self.close().await;
        }
    }

    /// 处理新连接请求
    /// 创建到目标地址的路由并建立连接
    ///
    /// # 参数
    /// * `tunnel_package` - 包含连接信息的隧道包
    pub async fn do_connect(self: Arc<Self>, mut tunnel_package: TunnelPackage) {
        // 检查原生UDP是否启用
        if tunnel_package.protocol == PackageProtocol::NativeUdp
            && !self.agent_context.get_native_udp_enable().await {
            debug!("原生UDP未启用，忽略连接请求");
            return;
        }

        // 检查用户是否已登录
        if !*self.is_login.read().await {
            warn!("用户未登录，拒绝连接请求");
            return;
        }

        // 验证目标地址
        let target_address = match tunnel_package.target_address.as_deref() {
            Some(addr) if !addr.is_empty() => addr.to_string(),
            _ => {
                debug!("目标地址无效，发送关闭连接命令");
                tunnel_package.cmd = PackageCmd::CloseConnect;
                if !self.write_tunnel_package(&mut tunnel_package).await {
                    self.close().await;
                }
                return;
            }
        };

        // 验证源地址
        let source_address = match tunnel_package.source_address {
            Some(addr) => addr,
            None => {
                debug!("源地址无效");
                return;
            }
        };

        // 创建新路由
        let route = Route::new(
            tunnel_package.protocol,
            target_address.clone(),
            source_address.clone(),
            self.clone(),
            self.get_route_table().await
        ).await;

        // 对于TCP连接，立即建立连接
        if tunnel_package.protocol == PackageProtocol::TCP {
            route.connect().await;
        }

        // 将路由添加到路由表
        self.get_route_table().await.add_route(route).await;
        debug!("新路由已创建：{} -> {}", source_address, target_address);
    }

    /// 处理关闭连接请求
    /// 从路由表中移除指定的路由
    ///
    /// # 参数
    /// * `tunnel_package` - 包含连接信息的隧道包
    pub async fn do_close_connect(&self, tunnel_package: TunnelPackage) {
        let source_address = match tunnel_package.source_address {
            Some(addr) => addr,
            None => {
                debug!("关闭连接失败：源地址为空");
                return;
            }
        };

        let target_address = match tunnel_package.target_address {
            Some(addr) => addr,
            None => {
                debug!("关闭连接失败：目标地址为空");
                return;
            }
        };

        // 从路由表中移除路由
        self.get_route_table().await.remove_route(
            &source_address,
            &target_address,
            tunnel_package.protocol
        ).await;

        debug!("路由已移除：{} -> {} ({})", source_address, target_address,
               match tunnel_package.protocol {
                   PackageProtocol::TCP => "TCP",
                   PackageProtocol::UDP => "UDP",
                   PackageProtocol::NativeUdp => "NativeUDP",
                   PackageProtocol::NONE => "NONE",
               });
    }

    /// 处理数据传输请求
    /// 将数据转发到目标地址
    ///
    /// # 参数
    /// * `tunnel_package` - 包含传输数据的隧道包
    pub async fn do_tdata(self: Arc<Self>, mut tunnel_package: TunnelPackage) {
        // 检查用户是否已登录
        if !*self.is_login.read().await {
            debug!("用户未登录，拒绝数据传输");
            return;
        }

        // 提取数据
        let data = match tunnel_package.data {
            Some(data) => data,
            None => {
                debug!("数据传输失败：数据为空");
                return;
            }
        };

        // 提取源地址
        let source_address = match tunnel_package.source_address.clone() {
            Some(addr) => addr,
            None => {
                debug!("数据传输失败：源地址为空");
                return;
            }
        };

        // 提取目标地址
        let target_address = match tunnel_package.target_address.clone() {
            Some(addr) => addr,
            None => {
                debug!("数据传输失败：目标地址为空");
                return;
            }
        };

        // 统计上传流量
        if !self.add_user_traffic_upload(data.len()).await {
            debug!("添加用户上传流量失败");
            self.close().await;
            return;
        }

        let route_table = self.get_route_table().await;

        // 对于UDP协议，如果路由不存在则创建新路由
        if tunnel_package.protocol == PackageProtocol::UDP {
            if !route_table.contain_udp(&source_address).await {
                debug!("为UDP连接创建新路由：{} -> {}", source_address, target_address);
                let route = Route::new(
                    tunnel_package.protocol,
                    target_address.clone(),
                    source_address.clone(),
                    self.clone(),
                    route_table.clone()
                ).await;
                route_table.add_route(route).await;
            }
        }

        // 发送数据到目标地址
        if !route_table.send_data_to_target(
            source_address.clone(),
            target_address.clone(),
            tunnel_package.protocol,
            data
        ).await {
            // 如果是TCP且路由不存在，发送断开连接命令
            if tunnel_package.protocol == PackageProtocol::TCP {
                debug!("TCP路由不存在，发送断开连接命令：{} -> {}", source_address, target_address);
                tunnel_package.cmd = PackageCmd::CloseConnect;
                tunnel_package.data = None;
                if !self.write_tunnel_package(&mut tunnel_package).await {
                    self.close().await;
                }
            } else {
                debug!("UDP数据发送失败，路由不存在：{} -> {}", source_address, target_address);
            }
        }
    }
}

impl ServerHandler {
    /// 关闭连接并清理所有相关资源
    /// 包括：用户登录状态、WebSocket连接、后台任务、路由表等
    pub async fn close(&self) {
        debug!("断开客户端连接: {}", self.socket_addr);

        // 先获取需要的数据，避免在后续操作中持有锁
        let (uid_option, client_uuid) = {
            let uid = self.uid.read().await.clone();
            let uuid = self.client_uuid.read().await.clone();
            (uid, uuid)
        };

        // 更新用户离线状态
        if let Some(uid) = uid_option {
            self.agent_context.set_user_login(
                &uid,
                false,
                self.socket_addr.ip().to_string(),
                client_uuid.clone()
            ).await;
            debug!("用户 [{}] 已设置为离线状态", uid);
        }

        // 关闭WebSocket连接
        if let Some(mut writer) = self.client_writer.write().await.take() {
            if let Err(e) = writer.close().await {
                debug!("关闭WebSocket连接时出错: {}", e);
            }
        }

        // 停止所有后台任务 - 分别获取锁，避免同时持有多个锁
        let jobs_to_abort = {
            let mut jobs = Vec::new();

            if let Some(job) = self.login_job.write().await.take() {
                jobs.push(("登录检查任务", job));
            }
            if let Some(job) = self.ping_job.write().await.take() {
                jobs.push(("心跳任务", job));
            }
            if let Some(job) = self.read_buffer_job.write().await.take() {
                jobs.push(("读取缓冲区任务", job));
            }
            if let Some(job) = self.server_receiver_job.write().await.take() {
                jobs.push(("服务器接收器任务", job));
            }

            jobs
        };

        // 中止所有任务
        for (task_name, job) in jobs_to_abort {
            job.abort();
            debug!("{} 已停止", task_name);
        }

        // 删除服务器映射
        self.agent_context.server_mapper.del_mapper(
            &client_uuid,
            &self.server_uuid
        ).await;
        debug!("服务器映射已删除");

        // 删除路由表
        self.del_route_table().await;
        debug!("路由表已清理");

        debug!("客户端连接 {} 的所有资源已清理完成", self.socket_addr);
    }

    /// 写入隧道包到客户端
    /// 包括加密、流量统计等处理
    ///
    /// # 参数
    /// * `tunnel_package` - 要发送的隧道包
    ///
    /// # 返回值
    /// * `true` - 发送成功
    /// * `false` - 发送失败
    pub async fn write_tunnel_package(&self, tunnel_package: &mut TunnelPackage) -> bool {
        // 统计下载流量
        if let Some(data) = tunnel_package.data.as_ref() {
            if !self.add_user_traffic_download(data.len()).await {
                error!("添加用户下载流量失败");
                self.close().await;
                return false;
            }
        }

        // 获取加密密码
        let password = match self.passwd_md5.read().await.clone() {
            Some(pwd) => pwd,
            None => {
                error!("隧道写入失败：密码为空");
                self.close().await;
                return false;
            }
        };

        // 序列化隧道包
        let mut serialized_data = Vec::new();
        tunnel_package.to_byte_array(&mut serialized_data);

        // 加密数据
        let encrypted_data = match encrypt_package(password.as_bytes(), &serialized_data) {
            Ok(data) => data,
            Err(e) => {
                error!("隧道包加密失败: {}", e);
                return false;
            }
        };

        // 发送加密数据
        let mut client_writer_guard = self.client_writer.write().await;
        match client_writer_guard.as_mut() {
            Some(client_writer) => {
                match client_writer.send(Message::binary(encrypted_data)).await {
                    Ok(_) => {
                        debug!("隧道包发送成功，大小: {} 字节", serialized_data.len());
                        true
                    }
                    Err(e) => {
                        error!("隧道包发送失败: {}", e);
                        false
                    }
                }
            }
            None => {
                debug!("客户端写入器不可用");
                false
            }
        }
    }

    /// 添加用户下载流量统计
    ///
    /// # 参数
    /// * `bytes` - 下载的字节数
    ///
    /// # 返回值
    /// * `true` - 统计成功
    /// * `false` - 用户未登录，统计失败
    pub async fn add_user_traffic_download(&self, bytes: usize) -> bool {
        match *self.uid.read().await {
            Some(uid) => {
                self.agent_context.add_user_download(&uid, bytes).await;
                debug!("用户 [{}] 下载流量 +{} 字节", uid, bytes);
                true
            }
            None => {
                debug!("无法统计下载流量：用户未登录");
                false
            }
        }
    }

    /// 添加用户上传流量统计
    ///
    /// # 参数
    /// * `bytes` - 上传的字节数
    ///
    /// # 返回值
    /// * `true` - 统计成功
    /// * `false` - 用户未登录，统计失败
    pub async fn add_user_traffic_upload(&self, bytes: usize) -> bool {
        match *self.uid.read().await {
            Some(uid) => {
                self.agent_context.add_user_upload(&uid, bytes).await;
                debug!("用户 [{}] 上传流量 +{} 字节", uid, bytes);
                true
            }
            None => {
                debug!("无法统计上传流量：用户未登录");
                false
            }
        }
    }

    /// 转发数据到指定客户端
    /// 用于客户端之间的数据转发
    ///
    /// # 参数
    /// * `tunnel_package` - 包含转发数据的隧道包
    pub async fn forward_data_to_client(&self, mut tunnel_package: TunnelPackage) {
        // 提取并验证数据
        let data = match tunnel_package.data.as_mut() {
            Some(data) => data,
            None => {
                debug!("转发失败：数据为空");
                return;
            }
        };

        // 检查数据长度（前32字节为目标客户端UUID）
        if data.len() < 32 {
            debug!("转发失败：数据长度不足32字节");
            return;
        }

        // 统计上传流量
        if !self.add_user_traffic_upload(data.len()).await {
            debug!("添加上传流量失败");
            self.close().await;
            return;
        }

        // 提取目标客户端UUID
        let target_client_uuid = String::from_utf8_lossy(&data[..32]).to_string();

        // 获取当前客户端UUID，避免多次获取锁
        let current_client_uuid = self.client_uuid.read().await.clone();

        // 检查是否为自己（避免循环转发）
        if target_client_uuid == current_client_uuid {
            debug!("忽略发送给自己的转发请求");
            return;
        }

        // 替换前32字节为当前客户端UUID
        let current_uuid_bytes = current_client_uuid.as_bytes().to_vec();
        if current_uuid_bytes.len() > 32 {
            debug!("当前客户端UUID长度超过32字节");
            return;
        }

        // 用当前客户端UUID覆盖前32字节
        let uuid_len = current_uuid_bytes.len();
        data[0..uuid_len].copy_from_slice(&current_uuid_bytes);
        if uuid_len < 32 {
            // 如果UUID长度不足32字节，用0填充剩余部分
            data[uuid_len..32].fill(0);
        }

        // 序列化隧道包
        let mut serialized_data = Vec::new();
        tunnel_package.to_byte_array(&mut serialized_data);

        // 获取加密密码
        let password = match self.passwd_md5.read().await.clone() {
            Some(pwd) => pwd,
            None => {
                warn!("转发失败：密码为空");
                return;
            }
        };

        // 统计下载流量
        if !self.add_user_traffic_download(serialized_data.len()).await {
            debug!("添加下载流量失败");
            self.close().await;
            return;
        }

        // 加密并转发数据
        match encrypt_package(password.as_bytes(), &serialized_data) {
            Ok(encrypted_data) => {
                if self.agent_context.server_mapper.forward_data(&target_client_uuid, encrypted_data).await {
                    debug!("数据已转发到客户端: {}", target_client_uuid);
                } else {
                    debug!("数据转发失败，目标客户端不存在或连接不可用: {}", target_client_uuid);
                }
            }
            Err(e) => {
                error!("转发数据加密失败: {}", e);
            }
        }
    }
}

/// 处理隧道包的主要分发函数
/// 根据包命令类型调用相应的处理函数
///
/// # 参数
/// * `tunnel_package` - 要处理的隧道包
/// * `server_handler` - 服务器处理器实例
async fn handler_tunnel_package(tunnel_package: TunnelPackage, server_handler: Arc<ServerHandler>) {
    match tunnel_package.cmd {
        // 用户登录请求
        PackageCmd::Login => {
            debug!("处理登录请求");
            if let Some(user_password) = server_handler.do_login(tunnel_package).await {
                // 启动登录状态检查任务
                let server_handler_clone = server_handler.clone();
                let _ = server_handler.login_job.write().await.insert(spawn(async move {
                    server_handler_clone.do_check_login(user_password).await;
                }));
            }
        }
        // 新建连接请求
        PackageCmd::NewConnect => {
            debug!("处理新建连接请求");
            server_handler.do_connect(tunnel_package).await;
        }
        // 关闭连接请求
        PackageCmd::CloseConnect => {
            debug!("处理关闭连接请求");
            server_handler.do_close_connect(tunnel_package).await;
        }
        // 数据传输
        PackageCmd::TData => {
            debug!("处理数据传输");
            server_handler.do_tdata(tunnel_package).await;
        }
        // 心跳检测
        PackageCmd::PING => {
            debug!("处理PING请求");
            server_handler.do_ping(tunnel_package).await;
        }
        // 客户端间转发命令
        PackageCmd::CNewConnect | PackageCmd::CCloseConnect | PackageCmd::CTdata => {
            debug!("处理客户端转发请求: {:?}", tunnel_package.cmd);
            server_handler.forward_data_to_client(tunnel_package).await;
        }
        // 以下命令通常由服务器发送，客户端接收时无需处理
        PackageCmd::LoginSuccess => {
            debug!("收到登录成功响应（通常不应在服务器端处理）");
        }
        PackageCmd::LoginFail => {
            debug!("收到登录失败响应（通常不应在服务器端处理）");
        }
        PackageCmd::ProtocolError => {
            warn!("收到协议错误响应");
        }
        PackageCmd::PONG => {
            debug!("收到PONG响应（通常不应在服务器端处理）");
        }
        PackageCmd::NONE => {
            warn!("收到空命令包");
        }
    }
}
