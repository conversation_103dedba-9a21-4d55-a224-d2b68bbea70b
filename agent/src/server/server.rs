use crate::context::context::AgentContext;
use crate::server::server_handler::ServerHandler;
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::spawn;
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use warp::http::StatusCode;
use warp::Filter;

/// Agent服务器结构体
///
/// 负责管理WebSocket服务器的生命周期，包括启动、停止和连接处理。
/// 该服务器专门用于处理客户端的WebSocket连接请求，并将连接转发给ServerHandler进行处理。
///
/// # 字段说明
/// * `agent_context` - Agent上下文的共享引用，包含所有必要的配置和状态信息
/// * `tcp_accept_job` - TCP连接接受任务的句柄，用于管理服务器的运行状态
/// * `port` - 当前服务器绑定的端口号，-1表示未启动
pub struct AgentServer {
    /// Agent上下文，包含节点配置、用户管理、路由等核心功能
    agent_context: Arc<AgentContext>,
    /// TCP连接接受任务句柄，用于控制服务器的启动和停止
    tcp_accept_job: RwLock<Option<JoinHandle<()>>>,
    /// 当前绑定的端口号，-1表示服务器未启动
    port: RwLock<i32>,
}

impl AgentServer {
    /// 创建一个新的AgentServer实例
    ///
    /// 初始化一个新的Agent服务器，但不立即启动。服务器将处于未启动状态，
    /// 端口设置为-1，TCP接受任务为None。
    ///
    /// # 参数
    /// * `agent_context` - Agent上下文的Arc引用，包含所有必要的配置信息
    ///
    /// # 返回值
    /// 返回一个新创建的AgentServer实例
    ///
    /// # 示例
    /// ```rust
    /// let agent_context = Arc::new(AgentContext::new(...));
    /// let server = AgentServer::new(agent_context);
    /// ```
    pub fn new(agent_context: Arc<AgentContext>) -> Self {
        Self {
            agent_context,
            tcp_accept_job: RwLock::new(None),
            port: RwLock::new(-1), // -1表示服务器未启动
        }
    }

    /// 启动服务器并绑定到指定端口
    ///
    /// 启动WebSocket服务器，监听指定端口上的连接请求。如果服务器已经在相同端口上运行，
    /// 则直接返回成功。服务器支持WebSocket连接，路径格式为 `/p/{password_md5}`。
    ///
    /// # 参数
    /// * `port` - 要绑定的端口号
    ///
    /// # 返回值
    /// * `Ok(())` - 服务器启动成功
    /// * `Err(String)` - 启动失败，包含错误信息和端口号
    ///
    /// # 错误处理
    /// 如果端口绑定失败（如端口被占用），将返回包含详细错误信息的字符串
    ///
    /// # 示例
    /// ```rust
    /// match server.start(8080).await {
    ///     Ok(()) => println!("服务器启动成功"),
    ///     Err(e) => eprintln!("服务器启动失败: {}", e),
    /// }
    /// ```
    pub async fn start(&self, port: i32) -> Result<(), String> {
        // 检查是否已经在相同端口上运行
        if port == *self.port.read().await {
            return Ok(());
        }

        let agent_context = self.agent_context.clone();

        // 创建WebSocket路由
        // 路径格式: /p/{password_md5}
        // 其中password_md5是用户密码的MD5哈希值，用于身份验证
        let ws_route = warp::path("p")
            .and(warp::ws()) // WebSocket升级过滤器
            .and(warp::path::param()) // 捕获路径参数（password_md5）
            .and(warp::addr::remote()) // 获取客户端源地址
            .and(warp::any().map(move || agent_context.clone())) // 传递agent_context
            .map(
                |ws: warp::ws::Ws,
                 path_param: String,
                 addr: Option<SocketAddr>,
                 agent_context: Arc<AgentContext>| {
                    // WebSocket连接升级处理
                    ws.on_upgrade(move |websocket| async move {
                        // 为每个连接创建独立的处理任务
                        let _ = spawn(ServerHandler::handle_connection(
                            websocket,
                            path_param,    // 密码MD5哈希
                            addr,          // 客户端地址
                            agent_context, // Agent上下文
                        ))
                        .await;
                    })
                },
            );

        // 创建非WebSocket请求的处理路由
        // 对于非WebSocket请求，返回BAD_REQUEST状态码和标识信息
        let other_route = warp::any().map(|| {
            warp::reply::with_status("FlyShadow Agent", StatusCode::BAD_REQUEST)
        });

        // 合并WebSocket路由和其他路由
        // 优先匹配WebSocket路由，如果不匹配则使用other_route
        let routes = ws_route.or(other_route);

        // 尝试绑定到指定端口
        // 使用try_bind_ephemeral允许系统选择可用端口（如果指定端口不可用）
        let bind_result = warp::serve(routes).try_bind_ephemeral(([0, 0, 0, 0], port as u16));

        match bind_result {
            Ok((_addr, server)) => {
                // 绑定成功，更新端口状态并启动服务器任务
                *self.port.write().await = port;

                // 创建服务器运行任务
                let server_task = spawn(async move {
                    server.await;
                });

                // 保存任务句柄以便后续管理
                let _ = self.tcp_accept_job.write().await.insert(server_task);

                Ok(())
            }
            Err(e) => {
                // 绑定失败，重置端口状态并返回错误
                *self.port.write().await = -1;
                Err(format!("服务器启动失败: {}, 端口: {}", e, port))
            }
        }
    }

    /// 关闭服务器
    ///
    /// 优雅地关闭WebSocket服务器，停止接受新连接并清理相关资源。
    /// 该方法会中止服务器任务并重置端口状态。
    ///
    /// # 行为说明
    /// 1. 将端口状态重置为-1（表示未启动）
    /// 2. 如果存在运行中的服务器任务，则中止该任务
    /// 3. 清理任务句柄
    ///
    /// # 注意事项
    /// - 该方法是异步的，因为需要获取写锁
    /// - 调用此方法后，服务器将完全停止，需要重新调用start()来重启
    /// - 已建立的WebSocket连接可能需要一些时间才能完全关闭
    ///
    /// # 示例
    /// ```rust
    /// server.close().await;
    /// println!("服务器已关闭");
    /// ```
    pub async fn close(&self) {
        // 重置端口状态为未启动状态
        *self.port.write().await = -1;

        // 如果存在运行中的服务器任务，则中止它
        if let Some(server_task) = self.tcp_accept_job.write().await.take() {
            server_task.abort();
        }
    }
}
