use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;

pub use tokio::io::AsyncReadExt;
use tokio::spawn;
use tokio::sync::RwLock;
use tokio::task::Jo<PERSON><PERSON><PERSON><PERSON>;
use tokio::time::{sleep, Instant};

use flyshadow_common::tunnel::tunnel_package::PackageProtocol;

use crate::route_table::router::Route;

/// 路由表结构体，负责管理网络连接的路由信息
///
/// 该结构体维护了TCP和UDP协议的路由映射关系，并提供了自动超时清理机制。
/// 主要功能包括：
/// - TCP连接的源地址到目标地址的路由映射
/// - UDP连接的源地址到路由的映射
/// - 自动检测和清理超时的连接
/// - 统一的数据发送接口
pub struct RouteTable {
    /// TCP路由映射表：键为"源地址===目标地址===协议"格式的字符串
    /// 值为对应的Route实例，用于处理TCP连接的数据转发
    tcp_source_addr_route_map: Arc<RwLock<HashMap<String, Route>>>,

    /// UDP路由映射表：键为"源地址===协议"格式的字符串
    /// 值为对应的Route实例，UDP连接可以向多个目标地址发送数据
    udp_target_addr_route_map: Arc<RwLock<HashMap<String, Route>>>,

    /// 路由表最后活跃时间，用于判断整个路由表是否超时
    active_time: Arc<RwLock<Instant>>,

    /// 后台超时检查任务句柄，定期清理超时的UDP路由
    timeout_job: JoinHandle<()>,
}

impl RouteTable {
    /// 构建TCP路由键
    ///
    /// TCP路由键格式：源地址===目标地址===协议类型
    ///
    /// # 参数
    /// - `source_addr`: 源地址
    /// - `target_addr`: 目标地址
    /// - `protocol`: 协议类型
    ///
    /// # 返回值
    /// 格式化的TCP路由键字符串
    fn build_tcp_route_key(source_addr: &str, target_addr: &str, protocol: &PackageProtocol) -> String {
        format!("{}==={}==={:?}", source_addr, target_addr, protocol)
    }

    /// 构建UDP路由键
    ///
    /// UDP路由键格式：源地址===协议类型
    /// UDP路由不包含目标地址，因为一个UDP路由可以向多个目标发送数据
    ///
    /// # 参数
    /// - `source_addr`: 源地址
    /// - `protocol`: 协议类型
    ///
    /// # 返回值
    /// 格式化的UDP路由键字符串
    fn build_udp_route_key(source_addr: &str, protocol: &PackageProtocol) -> String {
        format!("{}==={:?}", source_addr, protocol)
    }

    /// 创建新的路由表实例
    ///
    /// 该方法会初始化所有必要的数据结构，并启动一个后台任务来定期检查和清理超时的UDP路由。
    ///
    /// # 返回值
    /// 返回一个新的RouteTable实例
    pub fn new() -> RouteTable {
        // 初始化TCP路由映射表，使用Arc<RwLock<>>确保线程安全的并发访问
        let tcp_source_addr_route_map = Arc::new(RwLock::new(HashMap::<String, Route>::new()));

        // 初始化UDP路由映射表，使用Arc<RwLock<>>确保线程安全的并发访问
        let udp_target_addr_route_map = Arc::new(RwLock::new(HashMap::<String, Route>::new()));

        // 克隆UDP路由映射表的引用，用于后台超时检查任务
        let route_map_clone = udp_target_addr_route_map.clone();

        // 启动后台超时检查任务
        // 该任务每60秒运行一次，检查UDP路由是否超时并进行清理
        let timeout_job = spawn(async move {
            loop {
                // 等待60秒后进行下一次检查
                sleep(Duration::from_secs(60)).await;

                // 收集需要检查的路由，避免在持有锁时调用异步方法
                let routes_to_check = {
                    let read_guard = route_map_clone.read().await;
                    read_guard.iter().map(|(key, route)| (key.clone(), route.clone())).collect::<Vec<_>>()
                };

                // 在锁外检查超时状态
                let mut timeout_routes = Vec::new();
                for (key, route) in routes_to_check {
                    if route.is_timeout().await {
                        timeout_routes.push((key, route));
                    }
                }

                // 移除超时的路由并关闭相关连接
                if !timeout_routes.is_empty() {
                    let mut write_guard = route_map_clone.write().await;
                    for (key, route) in timeout_routes {
                        if write_guard.remove(&key).is_some() {
                            // 在锁外关闭连接，避免死锁
                            drop(write_guard);
                            route.close().await;
                            write_guard = route_map_clone.write().await;
                        }
                    }
                }
            }
        });

        RouteTable {
            tcp_source_addr_route_map,
            udp_target_addr_route_map,
            active_time: Arc::new(RwLock::new(Instant::now())),
            timeout_job,
        }
    }

    /// 向目标地址发送数据
    ///
    /// 根据协议类型（TCP或UDP）查找对应的路由并发送数据。
    /// 该方法会更新路由表的活跃时间，表示路由表仍在使用中。
    ///
    /// # 参数
    /// - `source_addr`: 源地址字符串
    /// - `target_addr`: 目标地址字符串
    /// - `package_protocol`: 数据包协议类型（TCP/UDP）
    /// - `data`: 要发送的数据字节数组
    ///
    /// # 返回值
    /// - `true`: 数据发送成功或协议类型不需要处理
    /// - `false`: 未找到对应的路由，数据发送失败
    pub async fn send_data_to_target(
        &self,
        source_addr: String,
        target_addr: String,
        package_protocol: PackageProtocol,
        data: Vec<u8>
    ) -> bool {
        // 更新路由表活跃时间，表示路由表正在被使用
        *self.active_time.write().await = Instant::now();

        match package_protocol {
            PackageProtocol::TCP => {
                // 构建TCP路由键：源地址===目标地址===协议类型
                let key = Self::build_tcp_route_key(&source_addr, &target_addr, &package_protocol);

                // 尝试获取对应的TCP路由并发送数据
                if let Some(route) = self.tcp_source_addr_route_map.read().await.get(&key) {
                    route.send_tcp_data_to_target(data).await;
                    true
                } else {
                    // 未找到对应的TCP路由
                    false
                }
            }
            PackageProtocol::UDP => {
                // 构建UDP路由键：源地址===协议类型
                // UDP路由可以向多个目标地址发送数据，所以键中不包含目标地址
                let key = Self::build_udp_route_key(&source_addr, &package_protocol);

                // 尝试获取对应的UDP路由并发送数据
                if let Some(route) = self.udp_target_addr_route_map.read().await.get(&key) {
                    route.send_udp_data_to_target(target_addr, data).await;
                    true
                } else {
                    // 未找到对应的UDP路由
                    false
                }
            }
            // 对于其他协议类型，直接返回true（不需要特殊处理）
            _ => true,
        }
    }

    /// 检查是否存在指定源地址的UDP路由
    ///
    /// # 参数
    /// - `source_addr`: 要检查的源地址
    ///
    /// # 返回值
    /// - `true`: 存在对应的UDP路由
    /// - `false`: 不存在对应的UDP路由
    pub async fn contain_udp(&self, source_addr: &String) -> bool {
        let key = Self::build_udp_route_key(source_addr, &PackageProtocol::UDP);
        self.udp_target_addr_route_map.read().await.contains_key(&key)
    }

    /// 添加新的路由到路由表
    ///
    /// 根据路由的协议类型，将路由添加到对应的映射表中。
    /// TCP路由使用源地址、目标地址和协议类型作为键，
    /// UDP路由使用源地址和协议类型作为键。
    ///
    /// # 参数
    /// - `route`: 要添加的路由实例
    pub async fn add_route(&self, route: Route) {
        // 更新路由表活跃时间
        *self.active_time.write().await = Instant::now();

        match route.protocol {
            PackageProtocol::TCP => {
                // TCP路由键格式：源地址===目标地址===协议类型
                let key = Self::build_tcp_route_key(&route.source_addr, &route.target_addr, &route.protocol);
                self.tcp_source_addr_route_map.write().await.insert(key, route);
            }
            PackageProtocol::UDP | PackageProtocol::NativeUdp => {
                // UDP路由键格式：源地址===协议类型
                // UDP和NativeUdp都使用相同的映射表
                let key = Self::build_udp_route_key(&route.source_addr, &route.protocol);
                self.udp_target_addr_route_map.write().await.insert(key, route);
            }
            // 其他协议类型不处理
            _ => {}
        }
    }

    /// 移除指定的TCP路由
    ///
    /// 根据源地址、目标地址和协议类型查找并移除对应的TCP路由。
    /// 移除时会调用路由的disconnect方法来正确关闭连接。
    ///
    /// # 参数
    /// - `source_addr`: 源地址
    /// - `target_addr`: 目标地址
    /// - `package_protocol`: 协议类型
    ///
    /// # 注意
    /// 目前此方法只处理TCP协议的路由移除
    pub async fn remove_route(&self, source_addr: &String, target_addr: &String, package_protocol: PackageProtocol) {
        // 更新路由表活跃时间
        *self.active_time.write().await = Instant::now();

        // 构建TCP路由键
        let key = Self::build_tcp_route_key(source_addr, target_addr, &package_protocol);

        // 尝试移除TCP路由并断开连接
        if let Some(route) = self.tcp_source_addr_route_map.write().await.remove(&key) {
            // 调用disconnect方法正确关闭连接并通知对端
            route.disconnect().await;
        }
    }

    /// 移除所有路由并关闭所有连接
    ///
    /// 该方法会清空所有的TCP和UDP路由映射表，并关闭所有相关的连接。
    /// 同时会中止后台的超时检查任务。
    /// 通常在路由表不再需要时调用此方法进行清理。
    pub async fn remove_all(&self) {
        // 清空TCP路由映射表并关闭所有TCP连接
        for (_, route) in self.tcp_source_addr_route_map.write().await.drain() {
            route.close().await;
        }

        // 清空UDP路由映射表并关闭所有UDP连接
        for (_, route) in self.udp_target_addr_route_map.write().await.drain() {
            route.close().await;
        }

        // 中止后台超时检查任务
        self.timeout_job.abort();
    }

    /// 检查路由表是否超时（无活动状态）
    ///
    /// 如果路由表在10分钟内没有任何活动（发送数据、添加路由等），
    /// 则认为该路由表已超时，可以被清理。
    ///
    /// # 返回值
    /// - `true`: 路由表已超时（10分钟内无活动）
    /// - `false`: 路由表仍在活跃状态
    pub async fn is_timeout(&self) -> bool {
        let now = Instant::now();
        // 检查距离上次活动是否超过10分钟（600秒）
        now.duration_since(*self.active_time.read().await) > Duration::from_secs(60 * 10)
    }
}

/// 实现 Drop trait 确保资源清理
///
/// 当RouteTable实例被销毁时，确保后台任务被正确中止，
/// 避免任务泄漏和资源浪费。
impl Drop for RouteTable {
    fn drop(&mut self) {
        // 中止后台超时检查任务
        // 注意：这里只是中止任务，实际的连接清理应该在调用remove_all()时完成
        self.timeout_job.abort();
    }
}
